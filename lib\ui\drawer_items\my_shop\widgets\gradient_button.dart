import 'package:flutter/material.dart';

class GradientButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isEnabled;
  final IconData? icon;
  final double height;
  final double? width;
  final List<Color>? gradientColors;
  final BorderRadius? borderRadius;
  final TextStyle? textStyle;

  const GradientButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
    this.icon,
    this.height = 56,
    this.width,
    this.gradientColors,
    this.borderRadius,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final defaultGradientColors = gradientColors ??
        (isDark
            ? [const Color(0xFF1A1A2E), const Color(0xFF16213E)]
            : [const Color(0xFF667eea), const Color(0xFF764ba2)]);

    return Container(
      width: width ?? double.infinity,
      height: height,
      decoration: BoxDecoration(
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: isEnabled && !isLoading
              ? defaultGradientColors
              : [Colors.grey[400]!, Colors.grey[500]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: isEnabled && !isLoading
            ? [
                BoxShadow(
                  color: defaultGradientColors.first.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        child: InkWell(
          onTap: isEnabled && !isLoading ? onPressed : null,
          borderRadius: borderRadius ?? BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isLoading)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                else if (icon != null)
                  Icon(
                    icon,
                    color: Colors.white,
                    size: 20,
                  ),
                if ((isLoading || icon != null) && text.isNotEmpty)
                  const SizedBox(width: 12),
                if (text.isNotEmpty)
                  Text(
                    text,
                    style: textStyle ??
                        const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.5,
                        ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class OutlineGradientButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isEnabled;
  final IconData? icon;
  final double height;
  final double? width;
  final List<Color>? gradientColors;
  final BorderRadius? borderRadius;
  final TextStyle? textStyle;

  const OutlineGradientButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
    this.icon,
    this.height = 56,
    this.width,
    this.gradientColors,
    this.borderRadius,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final defaultGradientColors = gradientColors ??
        (isDark
            ? [const Color(0xFF1A1A2E), const Color(0xFF16213E)]
            : [const Color(0xFF667eea), const Color(0xFF764ba2)]);

    return Container(
      width: width ?? double.infinity,
      height: height,
      decoration: BoxDecoration(
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: isEnabled && !isLoading
              ? defaultGradientColors
              : [Colors.grey[400]!, Colors.grey[500]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Container(
        margin: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          borderRadius: borderRadius ?? BorderRadius.circular(14),
          color: isDark ? Colors.grey[900] : Colors.white,
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: borderRadius ?? BorderRadius.circular(14),
          child: InkWell(
            onTap: isEnabled && !isLoading ? onPressed : null,
            borderRadius: borderRadius ?? BorderRadius.circular(14),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (isLoading)
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          defaultGradientColors.first,
                        ),
                      ),
                    )
                  else if (icon != null)
                    Icon(
                      icon,
                      color: defaultGradientColors.first,
                      size: 20,
                    ),
                  if ((isLoading || icon != null) && text.isNotEmpty)
                    const SizedBox(width: 12),
                  if (text.isNotEmpty)
                    ShaderMask(
                      shaderCallback: (bounds) => LinearGradient(
                        colors: defaultGradientColors,
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ).createShader(bounds),
                      child: Text(
                        text,
                        style: textStyle ??
                            const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.5,
                            ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
